using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;

namespace Marketplaces.Api.Responses;

public class PriceDto
{
    public int Id { get; set; }
    public int? MinAmount { get; set; }
    public string? Name { get; set; }
    public string? Image { get; set; }
    public NestedPriceDto? Ozon { get; set; }
    public NestedPriceDto? Yandex { get; set; }
    public NestedPriceDto? Wildberries { get; set; }
    public decimal? PurchasePrice { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public string? Size { get; set; }

    public void CalculateProfitFields(Nomenclature nomenclature, Shop shop)
    {
        if (Ozon != null && nomenclature.Ozon != null)
        {
            var ozonTax = shop.GetMarketplaceTax(Marketplace.Ozon);
            CalculateNestedProfitFields(Ozon, nomenclature.PurchasePrice, shop.BankTax, ozonTax);
        }

        if (Wildberries != null && nomenclature.Wildberries != null)
        {
            var wbTax = shop.GetMarketplaceTax(Marketplace.Wildberries);
            CalculateNestedProfitFields(Wildberries, nomenclature.PurchasePrice, shop.BankTax, wbTax);
        }

        if (Yandex != null && nomenclature.Yandex != null)
        {
            var yandexTax = shop.GetMarketplaceTax(Marketplace.Yandex);
            CalculateNestedProfitFields(Yandex, nomenclature.PurchasePrice, shop.BankTax, yandexTax);
        }
    }

    public void CalculateProfitFields(Shop shop)
    {
        if (Ozon != null)
        {
            var ozonTax = shop.GetMarketplaceTax(Marketplace.Ozon);
            CalculateNestedProfitFields(Ozon, PurchasePrice, shop.BankTax, ozonTax);
        }

        if (Wildberries != null)
        {
            var wbTax = shop.GetMarketplaceTax(Marketplace.Wildberries);
            CalculateNestedProfitFields(Wildberries, PurchasePrice, shop.BankTax, wbTax);
        }

        if (Yandex != null)
        {
            var yandexTax = shop.GetMarketplaceTax(Marketplace.Yandex);
            CalculateNestedProfitFields(Yandex, PurchasePrice, shop.BankTax, yandexTax);
        }
    }

    private static void CalculateNestedProfitFields(NestedPriceDto nestedDto, decimal? purchasePrice, decimal? bankTax, decimal? marketplaceTax)
    {
        if (nestedDto.BasePrice == null || purchasePrice == null || bankTax == null || marketplaceTax == null)
            return;

        decimal? salePrice = nestedDto.SalePrice;
        if (salePrice == null)
        {
            salePrice = nestedDto.BasePrice.Value;
            if (nestedDto.DiscountPercent.HasValue)
            {
                var discountMultiplier = 1 - (nestedDto.DiscountPercent.Value / 100);
                salePrice = nestedDto.BasePrice.Value * discountMultiplier;
            }
        }

        var bankCommissionAmount = salePrice * (bankTax.Value / 100);

        var marketplaceCommissionAmount = salePrice * (marketplaceTax.Value / 100);

        var revenue = salePrice - bankCommissionAmount - marketplaceCommissionAmount;

        var profit = revenue - purchasePrice.Value;

        var profitPercentage = purchasePrice.Value != 0 ? profit / purchasePrice.Value * 100 : 0;

        nestedDto.SalePrice = Math.Round(salePrice.Value, 2);
        nestedDto.BankCommissionAmount = Math.Round(bankCommissionAmount.Value, 2);
        nestedDto.MarketplaceCommissionAmount = Math.Round(marketplaceCommissionAmount.Value, 2);
        nestedDto.Revenue = Math.Round(revenue.Value, 2);
        nestedDto.Profit = Math.Round(profit.Value, 2);
        nestedDto.ProfitPercentage = Math.Round(profitPercentage.Value, 2);
    }
}