using Marketplaces.Api.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Marketplaces.Api.Requests;

public class CalculatePriceRequest
{
    [Required]
    public int NomenclatureId { get; set; }

    [Required]
    public decimal PurchasePrice { get; set; }

    [Required]
    public decimal BasePrice { get; set; }

    [Required]
    public decimal DiscountPercent { get; set; }

    [Required]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public Marketplace Marketplace { get; set; }
}
